export interface ErrorAnalysis {
  errorType: string;
  location: string;
  description: string;
  possibleCauses: string[];
  solutions: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  lineNumber?: number;
  codeContext?: {
    targetLine: string;
    contextLines: Array<{ number: number; content: string; isTarget: boolean }>;
  };
}

export interface ProgramFile {
  name: string;
  content: string;
  type: 'program' | 'error';
}

export interface ErrorAnalysisResult {
  summary: string;
  errors: ErrorAnalysis[];
  recommendations: string[];
  timestamp: string;
}

export interface SQLGenerationRequest {
  specification: string;
  databaseType?: 'mysql' | 'postgresql' | 'sqlite' | 'sqlserver';
  includeComments?: boolean;
}

export interface SQLGenerationResult {
  sql: string;
  explanation: string;
  tables: string[];
  operations: string[];
  timestamp: string;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  role: string;
  goal: string;
  prompt: string;
  tools: string[];
  utils: string[];
}

export interface AgentResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  timestamp: string;
}
