import openai from '@/lib/openai';
import { ErrorAnalysisResult, ProgramFile, Agent } from '@/types';
import { parseErrorFile, extractLineFromProgram } from '@/lib/utils';

export const errorAnalysisAgent: Agent = {
  id: 'error-analysis-agent',
  name: 'Agent d\'Analyse d\'Erreurs',
  description: 'Analyse les fichiers de programme et d\'erreur pour détecter les erreurs, leurs emplacements et proposer des solutions',
  role: 'Expert en analyse d\'erreurs et débogage de programmes',
  goal: 'Identifier, localiser et proposer des solutions pour les erreurs dans les programmes',
  prompt: `Tu es un expert en analyse d'erreurs et débogage de programmes. 
  Ton rôle est d'analyser les fichiers de programme et les fichiers d'erreur pour :
  1. Détecter et identifier les erreurs
  2. Localiser précisément où elles se produisent
  3. Expliquer les causes possibles
  4. Proposer des solutions concrètes et détaillées
  
  Tu dois fournir une analyse structurée et professionnelle avec des recommandations pratiques.`,
  tools: ['file-analysis', 'error-parsing', 'solution-generation'],
  utils: ['parseErrorFile', 'formatDate']
};

export class ErrorAnalysisService {
  private agent: Agent;

  constructor() {
    this.agent = errorAnalysisAgent;
  }

  async analyzeFiles(programFile: ProgramFile, errorFile: ProgramFile): Promise<ErrorAnalysisResult> {
    try {
      // Parse error file to extract structured error information
      const parsedErrors = parseErrorFile(errorFile.content);

      // Limit program file content to avoid token limit (keep first 5000 chars + last 1000 chars)
      let programContent = programFile.content;
      if (programContent.length > 6000) {
        const start = programContent.substring(0, 5000);
        const end = programContent.substring(programContent.length - 1000);
        programContent = start + '\n\n... [CONTENU TRONQUÉ] ...\n\n' + end;
      }

      const prompt = `${this.agent.prompt}

FICHIER PROGRAMME:
Nom: ${programFile.name}
Contenu (extrait):
${programContent}

FICHIER D'ERREUR:
Nom: ${errorFile.name}
Contenu:
${errorFile.content}

ERREURS PARSÉES:
${JSON.stringify(parsedErrors, null, 2)}

Analyse ces fichiers et fournis une réponse JSON structurée avec:
{
  "summary": "Résumé général de l'analyse",
  "errors": [
    {
      "errorType": "Type d'erreur",
      "location": "Emplacement précis",
      "description": "Description détaillée",
      "possibleCauses": ["cause1", "cause2"],
      "solutions": ["solution1", "solution2"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123
    }
  ],
  "recommendations": ["recommandation1", "recommandation2"]
}

IMPORTANT: Si tu détectes un numéro de ligne dans les erreurs, inclus-le dans le champ "lineNumber".`;

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.agent.prompt
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('Aucune réponse reçue de l\'API OpenAI');
      }

      // Try to parse JSON response
      let analysisResult: ErrorAnalysisResult;
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisResult = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback if no JSON found
          analysisResult = {
            summary: content,
            errors: [],
            recommendations: [],
            timestamp: new Date().toISOString()
          };
        }
      } catch (parseError) {
        // Fallback parsing
        analysisResult = {
          summary: content,
          errors: [],
          recommendations: [],
          timestamp: new Date().toISOString()
        };
      }

      analysisResult.timestamp = new Date().toISOString();

      // Ajouter le contexte de ligne pour chaque erreur qui a un numéro de ligne
      if (analysisResult.errors) {
        analysisResult.errors = analysisResult.errors.map(error => {
          if (error.lineNumber && error.lineNumber > 0) {
            const codeContext = extractLineFromProgram(programFile.content, error.lineNumber);
            return {
              ...error,
              codeContext
            };
          }
          return error;
        });
      }

      return analysisResult;

    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
      throw new Error(`Erreur lors de l'analyse des fichiers: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  getAgentInfo(): Agent {
    return this.agent;
  }
}
