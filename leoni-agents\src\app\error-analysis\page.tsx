'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { ErrorAnalysisResult, ProgramFile } from '@/types';
import { FileText, AlertCircle, CheckCircle, Clock, Bug, FileX } from 'lucide-react';
import { processFile, getAllSupportedTypes, getSupportedTypesDescription, isFileTypeSupported } from '@/lib/fileUtils';

export default function ErrorAnalysisPage() {
  const [programFile, setProgramFile] = useState<ProgramFile>({ name: '', content: '', type: 'program' });
  const [errorFile, setErrorFile] = useState<ProgramFile>({ name: '', content: '', type: 'error' });
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<ErrorAnalysisResult | null>(null);
  const [error, setError] = useState<string>('');


  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, fileType: 'program' | 'error') => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (fileType === 'program') {
          setProgramFile({ name: file.name, content, type: 'program' });
        } else {
          setErrorFile({ name: file.name, content, type: 'error' });
        }
      };
      reader.readAsText(file);
    }
  };

  const handleAnalyze = async () => {
    if (!programFile.content || !errorFile.content) {
      setError('Veuillez fournir les deux fichiers (programme et erreur)');
      return;
    }

    setIsAnalyzing(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/error-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          programFile,
          errorFile
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur lors de l\'analyse');
      }

      setResult(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de l\'analyse');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 bg-red-50';
      case 'HIGH': return 'text-orange-600 bg-orange-50';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50';
      case 'LOW': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Analyse d'Erreurs
        </h1>
        <p className="text-gray-600">
          Analysez vos fichiers de programme et d'erreur pour obtenir des diagnostics détaillés.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Fichier Programme
              </CardTitle>
              <CardDescription>
                Téléchargez ou collez le contenu de votre fichier programme
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Input
                  type="file"
                  accept=".txt,.c,.cpp,.ec,.log"
                  onChange={(e) => handleFileUpload(e, 'program')}
                  className="mb-2"
                />
                {programFile.name && (
                  <p className="text-sm text-green-600">✓ {programFile.name}</p>
                )}
              </div>
              <Textarea
                placeholder="Ou collez le contenu du fichier programme ici..."
                value={programFile.content}
                onChange={(e) => setProgramFile({ ...programFile, content: e.target.value })}
                className="min-h-[200px] font-mono text-sm"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Fichier d'Erreur
              </CardTitle>
              <CardDescription>
                Téléchargez ou collez le contenu de votre fichier d'erreur
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Input
                  type="file"
                  accept=".txt,.error,.log"
                  onChange={(e) => handleFileUpload(e, 'error')}
                  className="mb-2"
                />
                {errorFile.name && (
                  <p className="text-sm text-green-600">✓ {errorFile.name}</p>
                )}
              </div>
              <Textarea
                placeholder="Ou collez le contenu du fichier d'erreur ici..."
                value={errorFile.content}
                onChange={(e) => setErrorFile({ ...errorFile, content: e.target.value })}
                className="min-h-[200px] font-mono text-sm"
              />
            </CardContent>
          </Card>



          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !programFile.content || !errorFile.content}
            className="w-full"
            size="lg"
          >
            {isAnalyzing ? (
              <>
                <Clock className="w-5 h-5 mr-2 animate-spin" />
                Analyse en cours...
              </>
            ) : (
              <>
                <Bug className="w-5 h-5 mr-2" />
                Analyser les Erreurs
              </>
            )}
          </Button>

          {error && (
            <div className="bg-[#ff7514]/5 border border-[#ff7514]/20 rounded-lg p-4">
              <p className="text-[#ff7514]">{error}</p>
            </div>
          )}
        </div>

        <div>
          {result && (
            <Card className="mt-8">
              <CardHeader className="bg-[#002857] text-white">
                <CardTitle className="flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Résultats de l'Analyse
                </CardTitle>
                <CardDescription className="text-blue-100">
                  Analyse terminée le {new Date(result.timestamp).toLocaleString('fr-FR')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Summary */}
                <div>
                  <h3 className="font-semibold mb-2">Résumé</h3>
                  <p className="text-gray-700">{result.summary}</p>
                </div>

                {/* Errors */}
                {result.errors.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-3">Erreurs Détectées ({result.errors.length})</h3>
                    <div className="space-y-4">
                      {result.errors.map((error, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{error.errorType}</h4>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(error.severity)}`}>
                              {error.severity}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>Emplacement:</strong> {error.location}
                            {error.lineNumber && (
                              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                Ligne {error.lineNumber}
                              </span>
                            )}
                          </p>
                          <p className="text-sm mb-3">{error.description}</p>

                          {/* Contexte de code */}
                          {error.codeContext && (
                            <div className="mb-3">
                              <p className="text-sm font-medium mb-2">Contexte du code:</p>
                              <div className="bg-gray-900 text-gray-100 p-3 rounded text-xs font-mono overflow-x-auto">
                                {error.codeContext.contextLines.map((line) => (
                                  <div
                                    key={line.number}
                                    className={`flex ${line.isTarget ? 'bg-red-900/50' : ''}`}
                                  >
                                    <span className="text-gray-400 mr-3 w-8 text-right">
                                      {line.number}
                                    </span>
                                    <span className={line.isTarget ? 'text-red-300' : ''}>
                                      {line.content || ' '}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {error.possibleCauses.length > 0 && (
                            <div className="mb-3">
                              <p className="text-sm font-medium mb-1">Causes possibles:</p>
                              <ul className="text-sm text-gray-600 list-disc list-inside">
                                {error.possibleCauses.map((cause, i) => (
                                  <li key={i}>{cause}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                          
                          {error.solutions.length > 0 && (
                            <div>
                              <p className="text-sm font-medium mb-1">Solutions recommandées:</p>
                              <ul className="text-sm text-gray-600 list-disc list-inside">
                                {error.solutions.map((solution, i) => (
                                  <li key={i}>{solution}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Recommendations */}
                {result.recommendations.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Recommandations Générales</h3>
                    <ul className="space-y-1">
                      {result.recommendations.map((recommendation, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          {recommendation}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!result && !isAnalyzing && (
            <Card className="bg-white/60 backdrop-blur-xl border-0 shadow-xl rounded-3xl">
              <CardContent className="text-center py-16">
                <div className="relative mb-6">
                  <div className="w-24 h-24 mx-auto bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center">
                    <Bug className="w-12 h-12 text-gray-400" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
                </div>
                <h3 className="text-xl font-bold text-gray-700 mb-3">Prêt pour l'Analyse</h3>
                <p className="text-gray-500 max-w-md mx-auto leading-relaxed">
                  Les résultats de l'analyse apparaîtront ici une fois que vous aurez téléchargé vos fichiers et lancé l'analyse.
                </p>
                <div className="mt-6 flex justify-center space-x-4 text-sm text-gray-400">
                  <span>🔍 Détection automatique</span>
                  <span>📍 Localisation précise</span>
                  <span>💡 Solutions expertes</span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
