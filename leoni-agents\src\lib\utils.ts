import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

export function parseErrorFile(content: string): Array<{
  timestamp: string;
  level: string;
  message: string;
  task?: string;
  lineNumber?: number;
}> {
  const lines = content.split('\n').filter(line => line.trim());
  return lines.map((line, index) => {
    const match = line.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);
    if (match) {
      const [, timestamp, level, message] = match;
      const taskMatch = message.match(/task \[([^\]]+)\]/);

      // Essayer d'extraire le numéro de ligne du message d'erreur
      const lineMatch = message.match(/line[:\s]+(\d+)/i) ||
                       message.match(/ligne[:\s]+(\d+)/i) ||
                       message.match(/\[.*line[:\s]+(\d+)/i) ||
                       message.match(/caofors\.ec line: (\d+)/);

      return {
        timestamp,
        level,
        message,
        task: taskMatch ? taskMatch[1] : undefined,
        lineNumber: lineMatch ? parseInt(lineMatch[1]) : undefined
      };
    }
    return {
      timestamp: '',
      level: 'UNKNOWN',
      message: line,
      lineNumber: index + 1 // Numéro de ligne dans le fichier d'erreur
    };
  });
}

export function extractLineFromProgram(programContent: string, lineNumber: number, context: number = 2): {
  targetLine: string;
  contextLines: Array<{ number: number; content: string; isTarget: boolean }>;
} {
  const lines = programContent.split('\n');
  const targetLine = lines[lineNumber - 1] || '';

  const startLine = Math.max(0, lineNumber - context - 1);
  const endLine = Math.min(lines.length, lineNumber + context);

  const contextLines = [];
  for (let i = startLine; i < endLine; i++) {
    contextLines.push({
      number: i + 1,
      content: lines[i] || '',
      isTarget: i === lineNumber - 1
    });
  }

  return {
    targetLine,
    contextLines
  };
}
